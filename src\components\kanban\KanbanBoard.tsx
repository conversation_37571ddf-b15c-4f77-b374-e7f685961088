"use client";

import React, { useState, useEffect, useMemo } from "react";
import type {
  DragEndEvent,
  DragOverEvent,
  DragStartEvent,
  Active,
} from "@dnd-kit/core";
import {
  DndContext,
  PointerSensor,
  useSensor,
  useSensors,
  closestCorners,
  DragOverlay,
} from "@dnd-kit/core";
import { SortableContext, arrayMove } from "@dnd-kit/sortable";
import { createPortal } from "react-dom";
import type { KanbanColumn, KanbanTask, UserProfile } from "@/types/kanban";
import {
  getKanbanColumns,
  getKanbanTasks,
  addTask,
  updateTask,
  deleteTask,
  updateTaskOrderBatch,
  addTaskNote as apiAddTaskNote,
  getAssignableUsers,
  addColumn,
  updateColumn,
  deleteColumn,
  updateColumnOrderBatch,
  deleteTasksByColumn,
} from "@/lib/firebase/firestore";
import { hasRole } from "@/lib/utils";
import Column from "./Column";
import TaskModal from "./TaskModal";
import ColumnModal from "./ColumnModal";
import TaskDragOverlay from "./TaskDragOverlay";
import { useAuth } from "@/lib/auth-context";
import { Plus, Columns, Filter, Search } from "lucide-react";
import { Timestamp } from "firebase/firestore";

const KanbanBoard: React.FC = () => {
  const { user, loading: authLoading } = useAuth();
  const isAuthenticated = !!user;
  const [columns, setColumns] = useState<KanbanColumn[]>([]);
  const [tasks, setTasks] = useState<KanbanTask[]>([]);
  const [users, setUsers] = useState<UserProfile[]>([]);
  const [loadingData, setLoadingData] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const [isTaskModalOpen, setIsTaskModalOpen] = useState(false);
  const [selectedTask, setSelectedTask] = useState<KanbanTask | null>(null);
  const [newTaskInitialColumnId, setNewTaskInitialColumnId] = useState<
    string | undefined
  >();

  const [isColumnModalOpen, setIsColumnModalOpen] = useState(false);
  const [selectedColumn, setSelectedColumn] = useState<KanbanColumn | null>(
    null,
  );

  const [activeDragItem, setActiveDragItem] = useState<Active | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [priorityFilter, setPriorityFilter] = useState<string>("all");

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    }),
  );

  const isAdmin = useMemo(() => {
    return hasRole(user, "admin");
  }, [user]);

  const fetchData = async () => {
    if (!isAuthenticated) return;
    setLoadingData(true);
    setError(null);
    try {
      const [fetchedColumns, fetchedTasks, fetchedUsers] = await Promise.all([
        getKanbanColumns(),
        getKanbanTasks(),
        getAssignableUsers(),
      ]);
      setColumns(fetchedColumns.sort((a, b) => a.order - b.order));
      setTasks(fetchedTasks);
      setUsers(fetchedUsers);
    } catch (err) {
      console.error("Failed to fetch kanban data:", err);
      setError("Failed to load board data. Please try again.");
    }
    setLoadingData(false);
  };

  useEffect(() => {
    if (isAuthenticated) {
      fetchData();
    }
  }, [isAuthenticated, fetchData]);

  const filteredTasks = useMemo(() => {
    return tasks.filter((task) => {
      const matchesSearch = task.title
        .toLowerCase()
        .includes(searchQuery.toLowerCase());
      const matchesPriority =
        priorityFilter === "all" || task.priority === priorityFilter;
      return matchesSearch && matchesPriority;
    });
  }, [tasks, searchQuery, priorityFilter]);

  const tasksByColumn = useMemo(() => {
    const grouped: { [key: string]: KanbanTask[] } = {};
    columns.forEach((col) => (grouped[col.id] = []));
    filteredTasks.forEach((task) => {
      if (grouped[task?.columnId]) {
        grouped[task?.columnId]?.push(task);
      } else {
        if (columns.length > 0 && columns[0])
          grouped[columns[0].id]?.push(task);
      }
    });
    for (const columnId in grouped) {
      grouped[columnId]?.sort((a, b) => a.order - b.order);
    }
    return grouped;
  }, [filteredTasks, columns]);

  const columnIds = useMemo(() => columns.map((col) => col.id), [columns]);

  const handleOpenTaskModal = (task?: KanbanTask, columnId?: string) => {
    setSelectedTask(task ?? null);
    setNewTaskInitialColumnId(columnId);
    setIsTaskModalOpen(true);
  };

  const handleCloseTaskModal = () => {
    setIsTaskModalOpen(false);
    setSelectedTask(null);
    setNewTaskInitialColumnId(undefined);
  };

  const handleSaveTask = async (taskData: KanbanTask) => {
    if (!user) return;
    try {
      const userMap = new Map(users.map((u) => [u.uid, u]));
      const assignedUser = taskData.assignedTo
        ? userMap.get(taskData.assignedTo)
        : null;
      const taskToSave = {
        ...taskData,
        assignedUserName:
          assignedUser?.displayName ?? assignedUser?.email ?? null,
      };

      if (taskData.id) {
        await updateTask(taskData.id, taskToSave);
        setTasks((prev) =>
          prev.map((t) =>
            t.id === taskData.id
              ? { ...t, ...taskToSave, updatedAt: Timestamp.now() }
              : t,
          ),
        );
      } else {
        const { ...newTaskData } = taskToSave;
        const creator = userMap.get(user.uid);
        const tasksInColumn = tasks.filter(
          (t) => t?.columnId === taskData?.columnId,
        );
        const newDbTask = {
          ...newTaskData,
          createdBy: user.uid,
          createdByName:
            creator?.displayName ?? creator?.email ?? user.email ?? "User",
          order: tasksInColumn.length,
        };
        const newId = await addTask(
          newDbTask as Omit<
            KanbanTask,
            "id" | "createdAt" | "updatedAt" | "notes"
          >,
        );
        setTasks((prev) => [
          ...prev,
          {
            ...newDbTask,
            id: newId,
            createdAt: Timestamp.now(),
            updatedAt: Timestamp.now(),
            notes: [],
          } as KanbanTask,
        ]);
      }
      handleCloseTaskModal();
    } catch (err) {
      console.error("Failed to save task:", err);
      alert("Failed to save task. Please try again.");
    }
  };

  const handleDeleteTask = async (taskId: string) => {
    try {
      await deleteTask(taskId);
      setTasks((prev) => prev.filter((t) => t.id !== taskId));
      handleCloseTaskModal();
    } catch (err) {
      console.error("Failed to delete task:", err);
      alert("Failed to delete task. Please try again.");
    }
  };

  const handleAddTaskNote = async (taskId: string, noteText: string) => {
    if (!user) return;
    try {
      const userProfile = users.find((u) => u.uid === user.uid);
      const newNote = await apiAddTaskNote(
        taskId,
        noteText,
        user.uid,
        userProfile?.displayName ?? user.email ?? "User",
      );
      setTasks((prevTasks) =>
        prevTasks.map((t) => {
          if (t.id === taskId) {
            return {
              ...t,
              notes: [...(t.notes ?? []), newNote],
              updatedAt: Timestamp.now(),
            };
          }
          return t;
        }),
      );
      if (selectedTask && selectedTask.id === taskId) {
        setSelectedTask((prev) =>
          prev ? { ...prev, notes: [...(prev.notes ?? []), newNote] } : null,
        );
      }
    } catch (err) {
      console.error("Failed to add note:", err);
      throw err;
    }
  };

  const handleOpenColumnModal = (column?: KanbanColumn) => {
    setSelectedColumn(column ?? null);
    setIsColumnModalOpen(true);
  };

  const handleCloseColumnModal = () => {
    setIsColumnModalOpen(false);
    setSelectedColumn(null);
  };

  const handleSaveColumn = async (columnData: Omit<KanbanColumn, "id">) => {
    try {
      if (selectedColumn) {
        await updateColumn(selectedColumn.id, columnData);
        setColumns((prev) =>
          prev.map((col) =>
            col.id === selectedColumn.id ? { ...col, ...columnData } : col,
          ),
        );
      } else {
        const newId = await addColumn(columnData);
        setColumns((prev) => [...prev, { ...columnData, id: newId }]);
      }
      handleCloseColumnModal();
    } catch (err) {
      console.error("Failed to save column:", err);
      alert("Failed to save column. Please try again.");
    }
  };

  const handleDeleteColumn = async (columnId: string) => {
    const confirmDelete = window.confirm(
      "Are you sure you want to delete this column? This will also delete all tasks in this column.",
    );

    if (!confirmDelete) return;

    try {
      await deleteTasksByColumn(columnId);
      await deleteColumn(columnId);
      setColumns((prev) => prev.filter((col) => col.id !== columnId));
      setTasks((prev) => prev.filter((task) => task.columnId !== columnId));
    } catch (err) {
      console.error("Failed to delete column:", err);
      alert("Failed to delete column. Please try again.");
    }
  };

  const onDragStart = (event: DragStartEvent) => {
    setActiveDragItem(event.active);
  };

  const onDragOver = (event: DragOverEvent) => {
    const { active, over } = event;
    if (!over || !active) return;
    if (active.id === over.id) return;

    const activeType = active.data.current?.type;
    const overType = over.data.current?.type;

    if (
      activeType === "Task" &&
      overType === "Task" &&
      active.data.current?.task.columnId !== over.data.current?.task.columnId
    ) {
      const activeTask = active.data.current?.task as KanbanTask;
      const overTask = over.data.current?.task as KanbanTask;

      setTasks((prev) => {
        const activeIndex = prev.findIndex((t) => t.id === activeTask.id);
        if (activeIndex === -1) return prev;

        const updatedTasks = [...prev];
        const task = updatedTasks[activeIndex];
        if (task) {
          updatedTasks[activeIndex] = {
            ...task,
            columnId: overTask.columnId,
          };
        }
        return updatedTasks;
      });
    }
  };

  const onDragEnd = async (event: DragEndEvent) => {
    setActiveDragItem(null);
    const { active, over } = event;

    if (!over) return;

    const activeId = active.id as string;
    const overId = over.id as string;
    const activeType = active.data.current?.type;

    if (activeId === overId && activeType === "Task") return;

    if (activeType === "Column") {
      const oldIndex = columns.findIndex((col) => col.id === activeId);
      const newIndex = columns.findIndex((col) => col.id === overId);
      if (oldIndex !== newIndex) {
        const newColumnsOrder = arrayMove(columns, oldIndex, newIndex);
        const updates = newColumnsOrder.map((col, index) => ({
          id: col.id,
          order: index,
        }));
        setColumns(newColumnsOrder);
        try {
          await updateColumnOrderBatch(updates);
        } catch (err) {
          console.error("Failed to update column order:", err);
          fetchData();
        }
      }
      return;
    }

    if (activeType === "Task") {
      const activeTask = tasks.find((t) => t.id === activeId);
      if (!activeTask) return;

      let newColumnId = activeTask.columnId;
      const overIsColumn = over.data.current?.type === "Column";
      const overIsTask = over.data.current?.type === "Task";

      if (overIsColumn) {
        newColumnId = overId;
      } else if (overIsTask) {
        const overTask = tasks.find((t) => t.id === overId);
        if (overTask) newColumnId = overTask.columnId;
      }

      const tasksToUpdate: { id: string; order: number; columnId?: string }[] =
        [];

      if (activeTask.columnId === newColumnId) {
        // Reordering within the same column
        const columnTasks = tasks
          .filter((t) => t.columnId === newColumnId)
          .sort((a, b) => a.order - b.order);

        const oldIndex = columnTasks.findIndex((t) => t.id === activeId);
        let newIndex = columnTasks.findIndex((t) => t.id === overId);

        if (newIndex === -1) {
          newIndex = columnTasks.length - 1;
        }

        const reorderedTasks = arrayMove(columnTasks, oldIndex, newIndex);

        reorderedTasks.forEach((task, index) => {
          if (task.order !== index) {
            tasksToUpdate.push({ id: task.id, order: index });
          }
        });
      } else {
        // Moving to a different column
        const sourceColumnTasks = tasks
          .filter(
            (t) => t.columnId === activeTask.columnId && t.id !== activeId,
          )
          .sort((a, b) => a.order - b.order);

        const targetColumnTasks = tasks
          .filter((t) => t.columnId === newColumnId)
          .sort((a, b) => a.order - b.order);

        // Update source column orders
        sourceColumnTasks.forEach((task, index) => {
          if (task.order !== index) {
            tasksToUpdate.push({ id: task.id, order: index });
          }
        });

        // Find insertion point in target column
        let insertIndex = targetColumnTasks.length;
        if (overIsTask) {
          const overTask = tasks.find((t) => t.id === overId);
          if (overTask && overTask.columnId === newColumnId) {
            insertIndex = targetColumnTasks.findIndex((t) => t.id === overId);
            if (insertIndex === -1) insertIndex = targetColumnTasks.length;
          }
        }

        // Insert the moved task and update orders
        targetColumnTasks.splice(insertIndex, 0, {
          ...activeTask,
          columnId: newColumnId,
        });

        targetColumnTasks.forEach((task, index) => {
          tasksToUpdate.push({
            id: task.id,
            order: index,
            columnId: newColumnId,
          });
        });
      }

      // Optimistic UI update
      const updatedTasks = tasks.map((task) => {
        const update = tasksToUpdate.find((u) => u.id === task.id);
        if (update) {
          return {
            ...task,
            order: update.order,
            columnId: update.columnId ?? task.columnId,
          };
        }
        return task;
      });

      setTasks(updatedTasks);

      try {
        if (tasksToUpdate.length > 0) {
          await updateTaskOrderBatch(tasksToUpdate);
        }
      } catch (err) {
        console.error("Failed to update task order:", err);
        alert("Failed to move task. Please refresh and try again.");
        fetchData();
      }
    }
  };

  if (authLoading) {
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="h-12 w-12 animate-spin rounded-full border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return (
      <div className="flex h-screen items-center justify-center text-red-500">
        Please log in to view the Kanban board.
      </div>
    );
  }

  if (loadingData) {
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="animate-pulse space-y-4">
          <div className="h-8 w-64 rounded bg-gray-300"></div>
          <div className="flex space-x-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="h-96 w-80 rounded bg-gray-200"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex h-screen items-center justify-center text-red-500">
        {error}
      </div>
    );
  }

  const activeDragTask = activeDragItem?.data.current?.task as KanbanTask;

  return (
    <DndContext
      sensors={sensors}
      collisionDetection={closestCorners}
      onDragStart={onDragStart}
      onDragOver={onDragOver}
      onDragEnd={onDragEnd}
    >
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 p-4 dark:from-gray-900 dark:to-gray-800 md:p-6">
        <div className="mb-6 space-y-4">
          <div className="flex items-center justify-between">
            <h1 className="bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-3xl font-bold text-transparent">
              Game Dev Task Board
            </h1>
            <div className="flex space-x-2">
              <button
                onClick={() =>
                  handleOpenTaskModal(
                    undefined,
                    columns.length > 0 ? columns[0]?.id : undefined,
                  )
                }
                className="flex items-center rounded-lg bg-indigo-600 px-4 py-2 font-semibold text-white shadow-lg transition-all duration-200 hover:scale-105 hover:bg-indigo-700"
              >
                <Plus size={20} className="mr-2" /> Add Task
              </button>
              {isAdmin && (
                <button
                  onClick={() => handleOpenColumnModal()}
                  className="flex items-center rounded-lg bg-green-600 px-4 py-2 font-semibold text-white shadow-lg transition-all duration-200 hover:scale-105 hover:bg-green-700"
                >
                  <Columns size={20} className="mr-2" /> Add Column
                </button>
              )}
            </div>
          </div>

          <div className="flex flex-wrap items-center gap-4">
            <div className="relative">
              <Search
                size={20}
                className="absolute left-3 top-1/2 -translate-y-1/2 transform text-gray-400"
              />
              <input
                type="text"
                placeholder="Search tasks..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-64 rounded-lg border border-gray-300 py-2 pl-10 pr-4 transition-all focus:border-transparent focus:ring-2 focus:ring-indigo-500 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-100"
              />
            </div>

            <div className="flex items-center gap-2">
              <Filter size={20} className="text-gray-500" />
              <select
                aria-label="Priority Filter"
                value={priorityFilter}
                onChange={(e) => setPriorityFilter(e.target.value)}
                className="rounded-lg border border-gray-300 px-3 py-2 transition-all focus:border-transparent focus:ring-2 focus:ring-indigo-500 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-100"
              >
                <option value="all">All Priorities</option>
                <option value="critical">🔴 Critical</option>
                <option value="high">🟠 High</option>
                <option value="medium">🟡 Medium</option>
                <option value="low">🟢 Low</option>
              </select>
            </div>
          </div>
        </div>

        {columns.length === 0 ? (
          <div className="py-20 text-center">
            <div className="mb-4 animate-bounce">
              <Columns size={64} className="mx-auto text-gray-400" />
            </div>
            <p className="mb-4 text-lg text-gray-500 dark:text-gray-400">
              No columns configured.
            </p>
            {isAdmin && (
              <button
                onClick={() => handleOpenColumnModal()}
                className="rounded-lg bg-green-600 px-6 py-3 font-semibold text-white shadow-lg transition-all duration-200 hover:scale-105 hover:bg-green-700"
              >
                Create First Column
              </button>
            )}
          </div>
        ) : (
          <div className="scrollbar-thin scrollbar-thumb-gray-400 dark:scrollbar-thumb-gray-600 scrollbar-track-transparent flex space-x-6 overflow-x-auto pb-4">
            <SortableContext items={columnIds}>
              {columns.map((column) => (
                <Column
                  key={column.id}
                  column={column}
                  tasks={tasksByColumn[column.id] ?? []}
                  onTaskClick={(task) => handleOpenTaskModal(task)}
                  onAddTask={() => handleOpenTaskModal(undefined, column.id)}
                  onEditColumn={isAdmin ? handleOpenColumnModal : undefined}
                  onDeleteColumn={isAdmin ? handleDeleteColumn : undefined}
                  isAdmin={isAdmin}
                />
              ))}
            </SortableContext>
          </div>
        )}
      </div>

      {typeof window !== "undefined" &&
        createPortal(
          <DragOverlay>
            {activeDragTask && <TaskDragOverlay task={activeDragTask} />}
          </DragOverlay>,
          document.body,
        )}

      <TaskModal
        isOpen={isTaskModalOpen}
        onClose={handleCloseTaskModal}
        onSave={handleSaveTask}
        onDelete={handleDeleteTask}
        onAddNote={handleAddTaskNote}
        task={selectedTask}
        columns={columns}
        users={users}
        initialColumnId={newTaskInitialColumnId}
      />

      <ColumnModal
        isOpen={isColumnModalOpen}
        onClose={handleCloseColumnModal}
        onSave={handleSaveColumn}
        onDelete={isAdmin ? handleDeleteColumn : undefined}
        column={selectedColumn}
        existingColumns={columns}
      />
    </DndContext>
  );
};

export default KanbanBoard;
