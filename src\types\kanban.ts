import type { Timestamp } from "firebase/firestore";

export interface UserProfile {
  uid: string;
  email?: string | null;
  displayName?: string | null;
  photoURL?: string | null;
}

export interface KanbanColumn {
  id: string;
  title: string;
  order: number;
  color?: string;
}

export interface TaskNote {
  id: string;
  text: string;
  userId: string;
  userName?: string;
  timestamp: Timestamp;
}

export type TaskPriority = "low" | "medium" | "high" | "critical";

export interface KanbanTask {
  id: string;
  title: string;
  description?: string;
  columnId: string;
  assignedTo?: string | null;
  assignedUserName?: string | null;
  createdBy: string;
  createdByName?: string;
  createdAt: Timestamp;
  updatedAt: Timestamp;
  order: number;
  notes?: TaskNote[];
  priority: TaskPriority;
  dueDate?: Timestamp | null;
}
