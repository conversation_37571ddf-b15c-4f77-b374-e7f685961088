"use client";

import Link from "next/link";
import ProtectedRoute from "@/components/auth/protected-route";
import { useAuth } from "@/lib/auth-context";
import { useCallback, useEffect, useState } from "react";
import {
  addDoc,
  collection,
  doc,
  getDocs,
  limit,
  orderBy,
  query,
  serverTimestamp,
  updateDoc,
} from "firebase/firestore";
import { db } from "@/lib/firebase";
import { hasRole } from "@/lib/utils";

export default function DashboardPage() {
  return (
    <ProtectedRoute>
      <DashboardContent />
    </ProtectedRoute>
  );
}

function DashboardContent() {
  const { user, signOut } = useAuth();
  const [announcement, setAnnouncement] = useState("");
  const [editingAnnouncement, setEditingAnnouncement] = useState(false);

  const fetchAnnouncement = useCallback(async () => {
    try {
      if (!db) {
        console.log("Firebase not available, skipping announcement fetch");
        return;
      }
      const q = query(
        collection(db, "announcements"),
        orderBy("createdAt", "desc"),
        limit(1),
      );
      const snapshot = await getDocs(q);
      if (!snapshot.empty) {
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        const data = snapshot.docs[0].data();
        // eslint-disable-next-line @typescript-eslint/no-unsafe-argument
        setAnnouncement(data.content ?? "");
      }
    } catch (error) {
      console.error("Error fetching announcement:", error);
    }
  }, []);

  useEffect(() => {
    void fetchAnnouncement();
  }, [fetchAnnouncement]);

  const handleSaveAnnouncement = async () => {
    try {
      if (!db) {
        console.log("Firebase not available, cannot save announcement");
        return;
      }
      if (announcement) {
        // Check if there's an existing announcement
        const q = query(
          collection(db, "announcements"),
          orderBy("createdAt", "desc"),
          limit(1),
        );
        const snapshot = await getDocs(q);

        if (!snapshot.empty) {
          // Update existing announcement
          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          // @ts-ignore
          const docRef = doc(db, "announcements", snapshot.docs[0].id);
          await updateDoc(docRef, {
            content: announcement,
            updatedAt: serverTimestamp(),
          });
        } else {
          // Add new announcement
          await addDoc(collection(db, "announcements"), {
            content: announcement,
            createdAt: serverTimestamp(),
          });
        }
        setEditingAnnouncement(false); // Exit editing mode
        await fetchAnnouncement();
      }
    } catch (error) {
      console.error("Error saving announcement:", error);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8 flex flex-col md:flex-row md:items-center md:justify-between">
        <div>
          <h1 className="text-3xl font-bold text-primary">
            Developer Dashboard
          </h1>
          <p className="mt-1 text-secondary">Welcome back, {user?.email}</p>
        </div>
        <button
          onClick={() => signOut()}
          className="border-border-primary hover:bg-primary/10 mt-4 rounded-lg border bg-transparent px-4 py-2 text-primary transition md:mt-0"
        >
          Sign Out
        </button>
      </div>

      {/* Announcement Section */}
      <div className="mb-8 rounded-lg bg-secondary p-6">
        <h2 className="mb-4 text-xl font-semibold text-primary">
          Team Announcement
        </h2>
        {hasRole(user, "admin") || hasRole(user, "writer") ? (
          <>
            {editingAnnouncement ? (
              <>
                <textarea
                  aria-label="Announcement"
                  value={announcement}
                  onChange={(e) => setAnnouncement(e.target.value)}
                  className="border-border-primary w-full rounded-lg border bg-primary px-4 py-2 text-primary focus:outline-none focus:ring-2 focus:ring-primary"
                  rows={4}
                />
                <div className="mt-4 flex justify-end">
                  <button
                    onClick={handleSaveAnnouncement}
                    className="mr-2 rounded-lg bg-accent px-4 py-2 text-white transition hover:opacity-90"
                  >
                    Save
                  </button>
                  <button
                    onClick={() => setEditingAnnouncement(false)}
                    className="border-border-primary hover:bg-primary/10 rounded-lg border px-4 py-2 text-primary transition"
                  >
                    Cancel
                  </button>
                </div>
              </>
            ) : (
              <>
                {announcement ? (
                  <p className="text-secondary">Announcement: {announcement}</p>
                ) : (
                  <p className="text-secondary">No announcement yet.</p>
                )}
                <button
                  onClick={() => setEditingAnnouncement(true)}
                  className="border-border-primary hover:bg-primary/10 mt-4 rounded-lg border px-4 py-2 text-primary transition"
                >
                  Edit Announcement
                </button>
              </>
            )}
          </>
        ) : (
          <>
            {announcement ? (
              <p className="text-secondary">Announcement: {announcement}</p>
            ) : (
              <p className="text-secondary">No announcement yet.</p>
            )}
          </>
        )}
      </div>

      <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
        {user && hasRole(user, "admin") && (
          <DashboardCard
            title="Manage Users"
            description="Add, edit, and remove team members and their roles."
            icon="👥"
            href="/user-management"
          />
        )}
        {user && (hasRole(user, "admin") || hasRole(user, "writer")) && (
          <DashboardCard
            title="Manage Roadmap"
            description="Update and manage the public roadmap for ongoing projects."
            icon="🗺️"
            href="/roadmap-management"
          />
        )}
        {user && (hasRole(user, "admin") || hasRole(user, "writer")) && (
          <DashboardCard
            title="Manage Game Posts"
            description="Create, edit, and publish game posts for the community."
            icon="🎮"
            href="/game-management"
          />
        )}
        {user && (hasRole(user, "admin") || hasRole(user, "writer")) && (
          <DashboardCard
            title="Manage Blog Posts"
            description="Create, edit, and publish blog posts for the community."
            icon="📝"
            href="/internal-blog"
          />
        )}
        <DashboardCard
          title="Spreadsheets"
          description="Access and manage project spreadsheets and data."
          icon="📊"
          href="/spreadsheets"
        />
        <DashboardCard
          title="Team Blog"
          description="Read internal team blog posts."
          icon="📖"
          href="/team-blog"
        />
        <DashboardCard
          title="Kanban Board"
          description="Manage tasks and projects."
          icon="📋"
          href="/kanban"
        />
      </div>

      {/* Recent Activity Section */}
      <div className="mt-12">
        <h2 className="mb-4 text-xl font-semibold text-primary">
          Recent Activity
        </h2>
        <div className="rounded-lg bg-secondary p-6">
          {/* Activity feed items would go here */}
          <p className="text-secondary">No recent activity to display.</p>
        </div>
      </div>
    </div>
  );
}

interface DashboardCardProps {
  title: string;
  description: string;
  icon: string;
  href: string;
}

function DashboardCard({ title, description, icon, href }: DashboardCardProps) {
  return (
    <Link
      href={href}
      className="block rounded-lg bg-secondary p-6 transition-shadow hover:shadow-lg"
    >
      <div className="mb-4 text-4xl">{icon}</div>
      <h3 className="mb-2 text-xl font-semibold text-primary">{title}</h3>
      <p className="text-secondary">{description}</p>
    </Link>
  );
}
