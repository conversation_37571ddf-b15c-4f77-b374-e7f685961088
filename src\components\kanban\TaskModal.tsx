"use client";

import React, { useState, useEffect, FormEvent } from "react";
import {
  KanbanTask,
  KanbanColumn,
  UserProfile,
  TaskPriority,
} from "@/types/kanban";
import { X, Plus, Calendar, Flag, User, FileText } from "lucide-react";
import { Timestamp } from "firebase/firestore";
import { formatDistanceToNow, format } from "date-fns";

interface TaskModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (task: KanbanTask) => Promise<void>;
  onDelete?: (taskId: string) => Promise<void>;
  onAddNote: (taskId: string, noteText: string) => Promise<void>;
  task?: KanbanTask | null;
  columns: KanbanColumn[];
  users?: UserProfile[];
  initialColumnId?: string;
}

const priorityOptions: {
  value: TaskPriority;
  label: string;
  color: string;
  icon: string;
}[] = [
  {
    value: "low",
    label: "Low",
    color: "text-green-600 bg-green-50 border-green-200",
    icon: "🟢",
  },
  {
    value: "medium",
    label: "Medium",
    color: "text-yellow-600 bg-yellow-50 border-yellow-200",
    icon: "🟡",
  },
  {
    value: "high",
    label: "High",
    color: "text-orange-600 bg-orange-50 border-orange-200",
    icon: "🟠",
  },
  {
    value: "critical",
    label: "Critical",
    color: "text-red-600 bg-red-50 border-red-200",
    icon: "🔴",
  },
];

const TaskModal: React.FC<TaskModalProps> = ({
  isOpen,
  onClose,
  onSave,
  onDelete,
  onAddNote,
  task,
  columns,
  users = [],
  initialColumnId,
}) => {
  const [title, setTitle] = useState("");
  const [description, setDescription] = useState("");
  const [columnId, setColumnId] = useState("");
  const [assignedTo, setAssignedTo] = useState<string>("");
  const [priority, setPriority] = useState<TaskPriority>("medium");
  const [dueDate, setDueDate] = useState<string>("");
  const [newNote, setNewNote] = useState("");
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (isOpen) {
      if (task) {
        setTitle(task.title);
        setDescription(task.description || "");
        setColumnId(task.columnId);
        setAssignedTo(task.assignedTo || "");
        setPriority(task.priority);
        setDueDate(
          task.dueDate ? format(task.dueDate.toDate(), "yyyy-MM-dd") : "",
        );
      } else {
        setTitle("");
        setDescription("");
        setColumnId(initialColumnId || columns[0]?.id || "");
        setAssignedTo("");
        setPriority("medium");
        setDueDate("");
      }
      setNewNote("");
    }
  }, [isOpen, task, initialColumnId, columns]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!title.trim() || !columnId) return;

    setLoading(true);
    try {
      const taskData: KanbanTask = {
        id: task?.id || "",
        title: title.trim(),
        description: description.trim() || undefined,
        columnId,
        assignedTo: assignedTo || null,
        priority,
        dueDate: dueDate ? Timestamp.fromDate(new Date(dueDate)) : null,
        createdBy: task?.createdBy || "",
        createdAt: task?.createdAt || Timestamp.now(),
        updatedAt: Timestamp.now(),
        order: task?.order || 0,
        notes: task?.notes || [],
      };

      await onSave(taskData);
    } catch (error) {
      console.error("Failed to save task:", error);
    }
    setLoading(false);
  };

  const handleAddNote = async () => {
    if (!newNote.trim() || !task) return;
    try {
      await onAddNote(task.id, newNote.trim());
      setNewNote("");
    } catch (error) {
      console.error("Failed to add note:", error);
    }
  };

  const handleDelete = async () => {
    if (!task || !onDelete) return;
    const confirmed = window.confirm(
      "Are you sure you want to delete this task?",
    );
    if (confirmed) {
      setLoading(true);
      try {
        await onDelete(task.id);
      } catch (error) {
        console.error("Failed to delete task:", error);
      }
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="animate-in fade-in fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 duration-200">
      <div className="animate-in slide-in-from-bottom-4 m-4 max-h-[90vh] w-full max-w-2xl overflow-y-auto rounded-lg bg-white p-6 shadow-xl duration-300 dark:bg-gray-800">
        <div className="mb-6 flex items-center justify-between">
          <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-100">
            {task ? "Edit Task" : "Create New Task"}
          </h2>
          <button
            aria-label="Close"
            onClick={onClose}
            className="text-gray-500 transition-colors hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
          >
            <X size={24} />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            <div className="md:col-span-2">
              <label className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
                <FileText size={16} className="mr-2 inline" />
                Task Title
              </label>
              <input
                type="text"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                className="w-full rounded-md border border-gray-300 px-3 py-2 transition-colors focus:border-indigo-500 focus:outline-none focus:ring-1 focus:ring-indigo-500 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-100"
                placeholder="Enter task title"
                required
              />
            </div>

            <div>
              <label className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
                <Flag size={16} className="mr-2 inline" />
                Priority
              </label>
              <select
                aria-label="Priority Selector"
                value={priority}
                onChange={(e) => setPriority(e.target.value as TaskPriority)}
                className="w-full rounded-md border border-gray-300 px-3 py-2 transition-colors focus:border-indigo-500 focus:outline-none focus:ring-1 focus:ring-indigo-500 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-100"
              >
                {priorityOptions.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.icon} {option.label}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
                <Calendar size={16} className="mr-2 inline" />
                Due Date
              </label>
              <input
                aria-label="Due Date"
                type="date"
                value={dueDate}
                onChange={(e) => setDueDate(e.target.value)}
                className="w-full rounded-md border border-gray-300 px-3 py-2 transition-colors focus:border-indigo-500 focus:outline-none focus:ring-1 focus:ring-indigo-500 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-100"
              />
            </div>

            <div>
              <label className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
                Column
              </label>
              <select
                aria-label="Column Selector"
                value={columnId}
                onChange={(e) => setColumnId(e.target.value)}
                className="w-full rounded-md border border-gray-300 px-3 py-2 transition-colors focus:border-indigo-500 focus:outline-none focus:ring-1 focus:ring-indigo-500 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-100"
                required
              >
                {columns.map((column) => (
                  <option key={column.id} value={column.id}>
                    {column.title}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
                <User size={16} className="mr-2 inline" />
                Assign To
              </label>
              <select
                aria-label="User Selector"
                value={assignedTo}
                onChange={(e) => setAssignedTo(e.target.value)}
                className="w-full rounded-md border border-gray-300 px-3 py-2 transition-colors focus:border-indigo-500 focus:outline-none focus:ring-1 focus:ring-indigo-500 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-100"
              >
                <option value="">Unassigned</option>
                {users.map((user) => (
                  <option key={user.uid} value={user.uid}>
                    {user.displayName || user.email}
                  </option>
                ))}
              </select>
            </div>
          </div>

          <div>
            <label className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
              Description
            </label>
            <textarea
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              rows={3}
              className="w-full rounded-md border border-gray-300 px-3 py-2 transition-colors focus:border-indigo-500 focus:outline-none focus:ring-1 focus:ring-indigo-500 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-100"
              placeholder="Task description (optional)"
            />
          </div>

          {task && task.notes && task.notes.length > 0 && (
            <div>
              <h3 className="mb-3 text-sm font-medium text-gray-700 dark:text-gray-300">
                Notes ({task.notes.length})
              </h3>
              <div className="max-h-40 space-y-2 overflow-y-auto">
                {task.notes.map((note) => (
                  <div
                    key={note.id}
                    className="rounded-md bg-gray-50 p-3 dark:bg-gray-700"
                  >
                    <div className="mb-1 flex items-center justify-between">
                      <span className="text-xs font-medium text-gray-600 dark:text-gray-300">
                        {note.userName}
                      </span>
                      <span className="text-xs text-gray-500 dark:text-gray-400">
                        {formatDistanceToNow(note.timestamp.toDate(), {
                          addSuffix: true,
                        })}
                      </span>
                    </div>
                    <p className="text-sm text-gray-700 dark:text-gray-200">
                      {note.text}
                    </p>
                  </div>
                ))}
              </div>
            </div>
          )}

          {task && (
            <div>
              <label className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
                Add Note
              </label>
              <div className="flex gap-2">
                <input
                  type="text"
                  value={newNote}
                  onChange={(e) => setNewNote(e.target.value)}
                  className="flex-1 rounded-md border border-gray-300 px-3 py-2 transition-colors focus:border-indigo-500 focus:outline-none focus:ring-1 focus:ring-indigo-500 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-100"
                  placeholder="Add a note..."
                />
                <button
                  aria-label="Add Note"
                  type="button"
                  onClick={handleAddNote}
                  disabled={!newNote.trim()}
                  className="rounded-md bg-gray-600 px-4 py-2 text-white transition-colors hover:bg-gray-700 disabled:opacity-50"
                >
                  <Plus size={16} />
                </button>
              </div>
            </div>
          )}

          <div className="flex justify-between pt-4">
            <div>
              {task && onDelete && (
                <button
                  type="button"
                  onClick={handleDelete}
                  disabled={loading}
                  className="rounded-md bg-red-600 px-4 py-2 text-sm font-semibold text-white transition-colors hover:bg-red-700 disabled:opacity-50"
                >
                  Delete Task
                </button>
              )}
            </div>
            <div className="flex space-x-2">
              <button
                type="button"
                onClick={onClose}
                className="rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-semibold text-gray-700 transition-colors hover:bg-gray-50 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={loading || !title.trim() || !columnId}
                className="rounded-md bg-indigo-600 px-4 py-2 text-sm font-semibold text-white transition-colors hover:bg-indigo-700 disabled:opacity-50"
              >
                {loading ? "Saving..." : task ? "Update Task" : "Create Task"}
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>
  );
};

export default TaskModal;
