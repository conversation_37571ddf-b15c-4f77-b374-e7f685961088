import React from "react";
import type { KanbanColumn, KanbanTask } from "@/types/kanban";
import TaskCard from "./TaskCard";
import { SortableContext, useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { PlusCircle, MoreVertical, Edit, Trash2 } from "lucide-react";
import { useState, useRef, useEffect } from "react";

interface ColumnProps {
  column: KanbanColumn;
  tasks: KanbanTask[];
  onTaskClick: (task: KanbanTask) => void;
  onAddTask: (columnId: string) => void;
  onEditColumn?: (column: KanbanColumn) => void;
  onDeleteColumn?: (columnId: string) => void;
  isAdmin?: boolean;
}

const Column: React.FC<ColumnProps> = ({
  column,
  tasks,
  onTaskClick,
  onAddTask,
  onEditColumn,
  onDeleteColumn,
  isAdmin = false,
}) => {
  const [showMenu, setShowMenu] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const menuRef = useRef<HTMLDivElement>(null);

  const {
    setNodeRef,
    attributes,
    listeners,
    transform,
    transition,
    isDragging,
    isOver,
  } = useSortable({
    id: column.id,
    data: { type: "Column", column },
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition: isDragging ? "none" : transition,
  };

  const taskIds = tasks.map((task) => task.id);

  const priorityCount = tasks.reduce(
    (acc, task) => {
      acc[task.priority] = (acc[task.priority] ?? 0) + 1;
      return acc;
    },
    {} as Record<string, number>,
  );

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setShowMenu(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  return (
    <div
      ref={setNodeRef}
      style={style}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      className={`flex h-full w-80 flex-shrink-0 flex-col rounded-lg p-4 transition-all duration-200 ${
        isDragging
          ? "rotate-1 scale-105 bg-gray-200 opacity-50 dark:bg-gray-700"
          : "bg-gray-100 dark:bg-gray-800/50"
      } ${
        isOver && !isDragging
          ? "bg-indigo-50 ring-2 ring-indigo-500 ring-opacity-50 dark:bg-indigo-900/20"
          : ""
      } ${isHovered ? "shadow-lg" : "shadow-sm"} `}
    >
      <div className="mb-4 flex items-center justify-between border-b border-gray-300 pb-3 dark:border-gray-700">
        <div
          {...attributes}
          {...listeners}
          className="flex-1 cursor-grab select-none transition-colors active:cursor-grabbing"
        >
          <h3 className="font-semibold text-gray-700 dark:text-gray-200">
            {column.title}
          </h3>
          <div className="mt-1 flex items-center gap-2">
            <span className="text-sm text-gray-500 dark:text-gray-400">
              {tasks.length} tasks
            </span>

            {priorityCount.critical && (
              <span className="rounded-full bg-red-100 px-2 py-0.5 text-xs text-red-700 dark:bg-red-900/30 dark:text-red-300">
                {priorityCount.critical} urgent
              </span>
            )}
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <button
            onClick={() => onAddTask(column.id)}
            className="text-indigo-600 transition-colors hover:text-indigo-800 dark:text-indigo-400 dark:hover:text-indigo-300"
            aria-label={`Add task to ${column.title}`}
          >
            <PlusCircle size={20} />
          </button>

          {isAdmin && (onEditColumn ?? onDeleteColumn) && (
            <div className="relative" ref={menuRef}>
              <button
                aria-label="Open menu"
                onClick={() => setShowMenu(!showMenu)}
                className="text-gray-500 transition-colors hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
              >
                <MoreVertical size={20} />
              </button>

              {showMenu && (
                <div className="animate-in slide-in-from-top-2 absolute right-0 top-8 z-20 w-32 rounded-md border border-gray-200 bg-white py-1 shadow-lg duration-200 dark:border-gray-600 dark:bg-gray-700">
                  {onEditColumn && (
                    <button
                      onClick={() => {
                        onEditColumn(column);
                        setShowMenu(false);
                      }}
                      className="flex w-full items-center px-3 py-2 text-sm text-gray-700 transition-colors hover:bg-gray-100 dark:text-gray-200 dark:hover:bg-gray-600"
                    >
                      <Edit size={16} className="mr-2" />
                      Edit
                    </button>
                  )}
                  {onDeleteColumn && (
                    <button
                      onClick={() => {
                        onDeleteColumn(column.id);
                        setShowMenu(false);
                      }}
                      className="flex w-full items-center px-3 py-2 text-sm text-red-600 transition-colors hover:bg-gray-100 dark:text-red-400 dark:hover:bg-gray-600"
                    >
                      <Trash2 size={16} className="mr-2" />
                      Delete
                    </button>
                  )}
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      <div className="scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-600 scrollbar-track-transparent flex-grow space-y-3 overflow-y-auto pr-1">
        <SortableContext items={taskIds}>
          {tasks.map((task) => (
            <TaskCard
              key={task.id}
              task={task}
              onClick={() => onTaskClick(task)}
            />
          ))}
        </SortableContext>
        {tasks.length === 0 && (
          <div className="py-8 text-center">
            <p className="mb-2 text-sm text-gray-500 dark:text-gray-400">
              No tasks yet
            </p>
            <button
              onClick={() => onAddTask(column.id)}
              className="text-xs text-indigo-600 transition-colors hover:text-indigo-800 dark:text-indigo-400 dark:hover:text-indigo-300"
            >
              Add the first task
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default Column;
