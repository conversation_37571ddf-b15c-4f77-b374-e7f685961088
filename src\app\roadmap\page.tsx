import { collection, getDocs, orderBy, query, where } from "firebase/firestore";
import { db } from "@/lib/firebase";

// Fetch roadmap data server-side
async function getRoadmapData() {
  try {
    const q = query(
      collection(db, "roadmap"),
      where("isPublic", "==", true),
      orderBy("date", "asc"),
    );

    const snapshot = await getDocs(q);

    interface Milestone {
      id: string;
      date: { seconds: number };
      title: string;
      description: string;
      status?: "completed" | "in-progress" | "planned";
      isPublic: boolean;
    }

    return snapshot.docs.map(
      (doc): Milestone =>
        ({
          id: doc.id,
          ...doc.data(),
        }) as Milestone,
    );
  } catch (error) {
    console.error("Error fetching roadmap:", error);
    return [];
  }
}

export default async function RoadmapPage() {
  const milestones = await getRoadmapData();

  return (
    <div className="container mx-auto px-4 py-12">
      <h1 className="mb-12 text-center text-4xl font-bold text-primary">
        Parallax Roadmap
      </h1>

      <div className="relative">
        {/* Vertical line */}
        <div className="absolute left-0 h-full w-1 -translate-x-1/2 transform bg-accent md:left-1/2"></div>

        <div className="space-y-16">
          {milestones.map((milestone, i) => (
            <div
              key={milestone.id}
              className={`relative flex flex-col items-center md:flex-row ${
                i % 2 === 0 ? "md:flex-row-reverse" : "md:flex-row"
              }`}
            >
              {/* Circle in the middle */}
              <div className="absolute left-0 h-6 w-6 -translate-x-1/2 transform rounded-full border-4 border-accent bg-primary md:left-1/2"></div>

              {/* Content */}
              <div
                className={`w-full md:w-5/12 ${
                  i % 2 === 0 ? "md:pr-8 md:text-right" : "md:pl-8"
                }`}
              >
                <div
                  className={`rounded-lg bg-secondary p-6 shadow-lg transition-transform hover:scale-105 ${
                    i % 2 === 0 ? "animate-fade-in-up" : "animate-fade-in-up"
                  }`}
                >
                  <span className="bg-accent/20 mb-2 inline-block rounded-full px-3 py-1 text-sm text-accent">
                    {new Date(
                      milestone.date.seconds * 1000,
                    ).toLocaleDateString()}
                  </span>
                  <h3 className="mb-2 text-xl font-bold text-primary">
                    {milestone.title}
                  </h3>
                  <p className="text-secondary">{milestone.description}</p>

                  {milestone.status && (
                    <div className="mt-4 flex items-center">
                      <div
                        className={`mr-2 h-2 w-2 rounded-full ${
                          milestone.status === "completed"
                            ? "bg-green-500"
                            : milestone.status === "in-progress"
                              ? "bg-yellow-500"
                              : "bg-blue-500"
                        }`}
                      ></div>
                      <span className="text-sm capitalize text-secondary">
                        {milestone.status.replace("-", " ")}
                      </span>
                    </div>
                  )}
                </div>
              </div>

              {/* Empty space for the other side */}
              <div className="w-0 md:w-5/12"></div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
