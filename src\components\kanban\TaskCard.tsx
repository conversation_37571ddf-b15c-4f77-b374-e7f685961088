import React from "react";
import type { KanbanTask, TaskPriority } from "@/types/kanban";
import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import {
  GripVertical,
  UserCircle,
  MessageSquare,
  Calendar,
  Flag,
} from "lucide-react";
import { formatDistanceToNow } from "date-fns";

interface TaskCardProps {
  task: KanbanTask;
  onClick: () => void;
  isDragOverlay?: boolean;
}

const priorityConfig: Record<
  TaskPriority,
  { color: string; bgColor: string; borderColor: string; icon: string }
> = {
  low: {
    color: "text-green-600 dark:text-green-400",
    bgColor: "bg-green-50 dark:bg-green-900/20",
    borderColor: "border-green-200 dark:border-green-800",
    icon: "🟢",
  },
  medium: {
    color: "text-yellow-600 dark:text-yellow-400",
    bgColor: "bg-yellow-50 dark:bg-yellow-900/20",
    borderColor: "border-yellow-200 dark:border-yellow-800",
    icon: "🟡",
  },
  high: {
    color: "text-orange-600 dark:text-orange-400",
    bgColor: "bg-orange-50 dark:bg-orange-900/20",
    borderColor: "border-orange-200 dark:border-orange-800",
    icon: "🟠",
  },
  critical: {
    color: "text-red-600 dark:text-red-400",
    bgColor: "bg-red-50 dark:bg-red-900/20",
    borderColor: "border-red-200 dark:border-red-800",
    icon: "🔴",
  },
};

const TaskCard: React.FC<TaskCardProps> = ({
  task,
  onClick,
  isDragOverlay = false,
}) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({
    id: task.id,
    data: { type: "Task", task },
    disabled: isDragOverlay,
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition: isDragging ? "none" : transition,
  };

  const priority = priorityConfig[task.priority];
  const isOverdue =
    task.dueDate && task.dueDate.toMillis() < Date.now() && task.dueDate;

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`group mb-3 cursor-pointer rounded-lg border-l-4 bg-white p-3 shadow-sm transition-all duration-200 hover:scale-[1.02] hover:shadow-md ${priority.borderColor} ${isDragging ? "rotate-3 scale-105 opacity-50" : ""} ${isDragOverlay ? "rotate-2 scale-105 shadow-2xl" : ""} dark:hover:bg-gray-750 dark:bg-gray-800`}
      onClick={onClick}
    >
      <div className="flex items-start justify-between">
        <div className="min-w-0 flex-1">
          <div className="mb-2 flex items-center gap-2">
            <span className="text-lg leading-none">{priority.icon}</span>
            <h4 className="line-clamp-2 text-sm font-medium text-gray-800 dark:text-gray-100">
              {task.title}
            </h4>
          </div>

          {task.description && (
            <p className="mb-2 line-clamp-2 text-xs text-gray-500 dark:text-gray-400">
              {task.description}
            </p>
          )}

          {task.dueDate && (
            <div
              className={`mb-2 flex items-center gap-1 text-xs ${
                isOverdue
                  ? "text-red-600 dark:text-red-400"
                  : "text-gray-500 dark:text-gray-400"
              }`}
            >
              <Calendar size={12} />
              <span>
                {isOverdue ? "Overdue " : ""}
                {formatDistanceToNow(task.dueDate.toDate(), {
                  addSuffix: true,
                })}
              </span>
            </div>
          )}
        </div>

        <button
          {...attributes}
          {...listeners}
          className="touch-none p-1 text-gray-400 opacity-0 transition-opacity hover:text-gray-600 group-hover:opacity-100 dark:hover:text-gray-300"
          aria-label="Drag task"
        >
          <GripVertical size={16} />
        </button>
      </div>

      <div className="flex items-center justify-between text-xs">
        <div className="flex items-center gap-2">
          {task.assignedUserName && (
            <div className="flex items-center gap-1 text-gray-500 dark:text-gray-400">
              <UserCircle size={12} />
              <span className="max-w-20 truncate">{task.assignedUserName}</span>
            </div>
          )}

          <div className={`flex items-center gap-1 ${priority.color}`}>
            <Flag size={12} />
            <span className="capitalize">{task.priority}</span>
          </div>
        </div>

        {task.notes && task.notes.length > 0 && (
          <div className="flex items-center gap-1 text-gray-500 dark:text-gray-400">
            <MessageSquare size={12} />
            <span>{task.notes.length}</span>
          </div>
        )}
      </div>
    </div>
  );
};

export default TaskCard;
