"use client";

import { useCallback, useEffect, useState } from "react";
import {
  addDoc,
  collection,
  deleteDoc,
  doc,
  getDocs,
  orderBy,
  query,
  serverTimestamp,
  updateDoc,
} from "firebase/firestore";
import ProtectedRoute from "@/components/auth/protected-route";
import { db } from "@/lib/firebase";
import dynamic from "next/dynamic";
import { type BlogPost } from "@/lib/blog";

// Dynamic import for text editor to avoid SSR issues
const Editor = dynamic(() => import("@/components/blog/editor"), {
  ssr: false,
});

export default function InternalBlogPage() {
  return (
    <ProtectedRoute>
      <BlogEditorContent />
    </ProtectedRoute>
  );
}

function BlogEditorContent() {
  const [posts, setPosts] = useState<BlogPost[]>([]);
  const [loading, setLoading] = useState(true);
  const [title, setTitle] = useState("");
  const [content, setContent] = useState("");
  const [isPublic, setIsPublic] = useState(false);
  const [isTeamBlog, setIsTeamBlog] = useState(false); // New state variable
  const [saving, setSaving] = useState(false);
  const [editingPostId, setEditingPostId] = useState<string | null>(null); // New state variable

  const fetchPosts = useCallback(async () => {
    try {
      const q = query(collection(db, "posts"), orderBy("createdAt", "desc"));
      const postsSnapshot = await getDocs(q);
      const postsList = postsSnapshot.docs.map((doc) => ({
        id: doc.id,
        ...doc.data(),
        // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment,@typescript-eslint/no-unsafe-call,@typescript-eslint/no-unsafe-member-access
        createdAt: doc.data().createdAt?.toDate() ?? new Date(),
      })) as BlogPost[];
      setPosts(postsList);
    } catch (error) {
      console.error("Error fetching posts:", error);
    } finally {
      setLoading(false);
    }
  }, [setLoading, setPosts]);

  useEffect(() => {
    void fetchPosts().then(() => {
      //
    });
  }, [fetchPosts]);

  const handleSavePost = async () => {
    if (!title || !content) {
      alert("Please enter a title and content");
      return;
    }

    setSaving(true);
    try {
      const postData = {
        title,
        content,
        isPublic,
        isTeamBlog, // Include isTeamBlog in the data
        updatedAt: serverTimestamp(),
      };

      if (editingPostId) {
        // Update existing post
        await updateDoc(doc(db, "posts", editingPostId), postData);
      } else {
        // Add new post
        await addDoc(collection(db, "posts"), {
          ...postData,
          createdAt: serverTimestamp(),
        });
      }

      // Reset form
      setTitle("");
      setContent("");
      setIsPublic(false);
      setIsTeamBlog(false); // Reset isTeamBlog
      setEditingPostId(null);

      // Refresh posts list
      await fetchPosts();

      alert("Post saved successfully!");
    } catch (error) {
      console.error("Error saving post:", error);
      alert(
        "Failed to save post: " +
          (error instanceof Error ? error.message : "Unknown error"),
      );
    } finally {
      setSaving(false);
    }
  };

  const handleEditPost = (post: BlogPost) => {
    setTitle(post.title);
    setContent(post.content ?? "");
    setIsPublic(post.isPublic);
    setIsTeamBlog(post.isTeamBlog ?? false); // Set isTeamBlog
    setEditingPostId(post.id ?? null);
  };

  const handleDeletePost = async (id: string) => {
    if (window.confirm("Are you sure you want to delete this post?")) {
      try {
        await deleteDoc(doc(db, "posts", id));
        await fetchPosts();
        alert("Post deleted successfully!");
      } catch (error) {
        console.error("Error deleting post:", error);
        alert(
          "Failed to delete post: " +
            (error instanceof Error ? error.message : "Unknown error"),
        );
      }
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="mb-8 text-3xl font-bold text-primary">Blog Management</h1>

      <div className="grid grid-cols-1 gap-8 lg:grid-cols-3">
        <div className="lg:col-span-2">
          <div className="rounded-lg bg-secondary p-6">
            <h2 className="mb-4 text-xl font-semibold text-primary">
              {editingPostId ? "Edit Post" : "Create New Post"}
            </h2>

            <div className="mb-4">
              <label className="mb-1 block text-sm font-medium text-secondary">
                Post Title
              </label>
              <input
                type="text"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                className="border-border-primary w-full rounded-lg border bg-primary px-4 py-2 text-primary focus:outline-none focus:ring-2 focus:ring-primary"
                placeholder="Enter post title"
              />
            </div>

            <div className="mb-4">
              <label className="mb-1 block text-sm font-medium text-secondary">
                Content
              </label>
              <Editor value={content} onChange={setContent} />
            </div>

            <div className="mb-6 flex items-center">
              <input
                type="checkbox"
                id="isPublic"
                checked={isPublic}
                onChange={(e) => setIsPublic(e.target.checked)}
                className="mr-2"
              />
              <label htmlFor="isPublic" className="text-secondary">
                Make this post public
              </label>
            </div>

            <div className="mb-6 flex items-center">
              <input
                type="checkbox"
                id="isTeamBlog"
                checked={isTeamBlog}
                onChange={(e) => setIsTeamBlog(e.target.checked)}
                className="mr-2"
              />
              <label htmlFor="isTeamBlog" className="text-secondary">
                Make this a team blog post
              </label>
            </div>

            <button
              onClick={handleSavePost}
              disabled={saving || !title || !content}
              className="rounded-lg bg-accent px-6 py-2 font-medium text-white transition hover:opacity-90 disabled:cursor-not-allowed disabled:opacity-70"
            >
              {saving
                ? "Saving..."
                : editingPostId
                  ? "Update Post"
                  : "Save Post"}
            </button>
          </div>
        </div>

        <div>
          <div className="rounded-lg bg-secondary p-6">
            <h2 className="mb-4 text-xl font-semibold text-primary">
              Recent Posts
            </h2>

            {loading ? (
              <div className="flex justify-center py-8">
                <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-t-2 border-accent"></div>
              </div>
            ) : posts.length === 0 ? (
              <p className="text-secondary">No posts yet.</p>
            ) : (
              <ul className="space-y-4">
                {posts.map((post) => (
                  <li
                    key={post.id}
                    className="border-border-primary border-b pb-3"
                  >
                    <h3 className="font-medium text-primary">{post.title}</h3>
                    <div className="mt-1 flex text-xs text-secondary">
                      <span
                        className={`rounded px-2 py-0.5 ${post.isPublic ? "bg-green-500/20 text-green-400" : "bg-yellow-500/20 text-yellow-400"}`}
                      >
                        {post.isPublic ? "Public" : "Private"}
                      </span>
                      <span
                        className={`ml-2 rounded px-2 py-0.5 ${post.isTeamBlog ? "bg-blue-500/20 text-blue-400" : ""}`}
                      >
                        {post.isTeamBlog ? "Team Blog" : ""}
                      </span>
                      <span className="ml-2">
                        {post.createdAt.toLocaleDateString() || "Unknown date"}
                      </span>
                    </div>
                    <div className="flex justify-end">
                      <button
                        onClick={() => handleEditPost(post)}
                        className="hover:text-accent/80 mr-4 text-accent"
                      >
                        Edit
                      </button>
                      <button
                        onClick={() => handleDeletePost(post.id || "")}
                        className="text-red-500 hover:text-red-400"
                      >
                        Delete
                      </button>
                    </div>
                  </li>
                ))}
              </ul>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
