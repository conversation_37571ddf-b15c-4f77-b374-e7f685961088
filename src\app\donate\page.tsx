import Image from "next/image";

export default function DonatePage() {
  return (
    <div className="container mx-auto px-4 py-12">
      <div className="mb-12 text-center">
        <h1 className="mb-4 text-4xl font-bold text-primary">
          Support Our Work
        </h1>
        <p className="mx-auto max-w-2xl text-secondary">
          Your contribution helps us create innovative games and continue our
          mission to push the boundaries of interactive entertainment.
        </p>
      </div>

      <div className="mb-16 grid grid-cols-1 gap-12 lg:grid-cols-2">
        <div className="rounded-xl bg-secondary p-8 shadow-lg">
          <h2 className="mb-4 text-2xl font-bold text-primary">
            Why Support Us?
          </h2>

          <ul className="space-y-5">
            {[
              {
                title: "Fund New Game Development",
                description:
                  "Help us create our next groundbreaking title by providing resources for development.",
              },
              {
                title: "Support Indie Innovation",
                description:
                  "Enable us to take creative risks and develop unique gaming experiences.",
              },
              {
                title: "Join Our Community",
                description:
                  "Become part of our journey and get special recognition in our games and community.",
              },
              {
                title: "Shape the Future",
                description:
                  "Higher tier supporters get to vote on future features and provide direct feedback to developers.",
              },
            ].map((item, i) => (
              <li key={i} className="flex">
                <div className="mt-1 flex-shrink-0">
                  <div className="flex h-5 w-5 items-center justify-center rounded-full bg-accent text-white">
                    ✓
                  </div>
                </div>
                <div className="ml-4">
                  <h3 className="font-semibold text-primary">{item.title}</h3>
                  <p className="text-secondary">{item.description}</p>
                </div>
              </li>
            ))}
          </ul>

          <div className="bg-primary/50 mt-8 rounded-lg p-6">
            <h3 className="mb-2 font-semibold text-primary">
              Current Funding Goal
            </h3>
            <div className="mb-2 h-4 w-full rounded-full bg-primary">
              <div
                className="h-4 rounded-full bg-accent"
                style={{ width: "65%" }}
              ></div>
            </div>
            <div className="flex justify-between text-sm text-secondary">
              <span>$32,500 raised</span>
              <span>$50,000 goal</span>
            </div>
            <p className="mt-4 text-primary">
              We&#39;re working towards funding our next major update with new
              content and features!
            </p>
          </div>
        </div>

        <div>
          <div className="mb-8 rounded-xl bg-secondary p-8 shadow-lg">
            <h2 className="mb-6 text-2xl font-bold text-primary">
              Support Tiers
            </h2>

            <div className="space-y-6">
              {[
                {
                  name: "Supporter",
                  price: "$5",
                  period: "monthly",
                  benefits: [
                    "Name in credits",
                    "Access to supporter Discord channel",
                    "Monthly development updates",
                  ],
                },
                {
                  name: "Enthusiast",
                  price: "$15",
                  period: "monthly",
                  benefits: [
                    "All Supporter benefits",
                    "Early access to beta builds",
                    "Exclusive in-game cosmetics",
                  ],
                  highlight: true,
                },
                {
                  name: "Patron",
                  price: "$30",
                  period: "monthly",
                  benefits: [
                    "All Enthusiast benefits",
                    "Vote on future features",
                    "Direct access to developers",
                    "Special mention in game credits",
                  ],
                },
              ].map((tier, i) => (
                <div
                  key={i}
                  className={`border ${
                    tier.highlight
                      ? "bg-accent/5 border-accent"
                      : "border-primary"
                  } rounded-lg p-5 transition-transform hover:scale-105`}
                >
                  <div className="mb-4 flex items-center justify-between">
                    <h3 className="text-xl font-bold text-primary">
                      {tier.name}
                    </h3>
                    <div className="text-right">
                      <span className="text-xl font-bold text-primary">
                        {tier.price}
                      </span>
                      <span className="text-sm text-secondary">
                        /{tier.period}
                      </span>
                    </div>
                  </div>

                  <ul className="mb-5 space-y-2">
                    {tier.benefits.map((benefit, j) => (
                      <li key={j} className="flex items-start">
                        <span className="mr-2 text-accent">✓</span>
                        <span className="text-secondary">{benefit}</span>
                      </li>
                    ))}
                  </ul>

                  <button
                    className={`w-full rounded-lg py-2 font-medium ${
                      tier.highlight
                        ? "bg-accent text-white"
                        : "border border-primary bg-primary text-primary hover:bg-secondary"
                    } transition`}
                  >
                    Select Plan
                  </button>
                </div>
              ))}
            </div>
          </div>

          <div className="rounded-xl bg-secondary p-8 shadow-lg">
            <h2 className="mb-4 text-2xl font-bold text-primary">
              One-time Donation
            </h2>
            <p className="mb-6 text-secondary">
              Prefer to make a one-time contribution? Every bit helps us
              continue our work!
            </p>

            <div className="mb-6 grid grid-cols-3 gap-3">
              {["$5", "$10", "$25", "$50", "$100", "Custom"].map(
                (amount, i) => (
                  <button
                    key={i}
                    className={`border py-2 ${
                      i === 2
                        ? "bg-accent/10 border-accent"
                        : "border-primary hover:border-accent"
                    } rounded-lg transition`}
                  >
                    {amount}
                  </button>
                ),
              )}
            </div>

            <button className="w-full rounded-lg bg-accent py-3 font-medium text-white transition hover:opacity-90">
              Donate Now
            </button>
          </div>
        </div>
      </div>

      <div className="text-center">
        <h2 className="mb-8 text-2xl font-bold text-primary">
          Our Amazing Supporters
        </h2>

        <div className="grid grid-cols-2 gap-8 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6">
          {Array.from({ length: 12 }).map((_, i) => (
            <div key={i} className="flex flex-col items-center">
              <div className="mb-2 h-16 w-16 overflow-hidden rounded-full bg-primary">
                <Image
                  src={`/images/supporter-${(i % 6) + 1}.jpg`}
                  alt={`Supporter ${i + 1}`}
                  width={64}
                  height={64}
                  className="h-full w-full object-cover"
                />
              </div>
              <p className="text-sm font-medium text-primary">
                Supporter {i + 1}
              </p>
              <p className="text-xs text-secondary">Patron since 2023</p>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
