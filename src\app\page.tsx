import Link from "next/link";
import Image from "next/image";
import { getLatestBlogPosts } from "@/lib/blog";
import { getPublicGames } from "@/lib/games";
import { ParallaxVideo } from "@/lib/useParallax"; // Import from utility

export default async function HomePage() {
  const games = await getPublicGames();
  const latestPosts = await getLatestBlogPosts();

  return (
    <main>
      {/* Hero Section */}
      <ParallaxVideo speed={0.8} src={"/videos/hero-background.mp4"} alt="Hero Background" containerClassName={"absolute inset-0 z-0"} />
      <section className="relative flex items-center justify-center overflow-hidden h-screen">
        <div className="container z-10 mx-auto animate-fade-in-up text-center">
          <h1 className="mb-6 text-5xl font-bold text-primary md:text-7xl">
            Parallax Interactive
          </h1>
          <p className="mx-auto mb-10 max-w-2xl text-xl text-secondary md:text-2xl">
            Creating immersive gaming experiences that push the boundaries of
            imagination
          </p>
          <div className="flex flex-wrap justify-center gap-4">
            <Link
              href="/join-us"
              className="rounded-lg border-2 border-accent bg-primary px-8 py-3 font-medium text-white transition hover:scale-110 hover:bg-accent"
            >
              Join The Team
            </Link>
            <Link
              href="https://discord.com/servers/parallax-interactive-1036739036427071498"
              target="_blank"
              className="rounded-lg border-2 border-accent bg-primary px-8 py-3 font-medium text-white transition hover:scale-110 hover:bg-accent"
            >
              Support Us
            </Link>
          </div>
        </div>
      </section>

      {/* Featured Projects */}
      <section className="bg-secondary py-20">
        <div className="container mx-auto px-4">
          <h2 className="mb-12 text-center text-3xl font-bold text-primary md:text-4xl">
            Featured Projects
          </h2>

          <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
            {games?.map((game) => (
              <div
                key={game.id}
                className="overflow-hidden rounded-lg bg-primary shadow-lg transition-transform hover:scale-105"
              >
                <div className="relative h-48 bg-gradient-to-r from-dark-orange to-bright-purple">
                  {game.imageUrl ? (
                    <Image
                      src={game.imageUrl}
                      alt={game.title}
                      fill
                      className="object-cover"
                    />
                  ) : (
                    <div className="code absolute inset-0 flex items-center justify-center text-center text-6xl text-white text-opacity-30">
                      {game.title}
                    </div>
                  )}
                </div>
                <div className="p-6">
                  <h3 className="mb-2 text-xl font-semibold text-primary">
                    {game.title}
                  </h3>
                  <p className="mb-4 text-secondary">{game.description}</p>
                  <Link
                    href={`/games/${game.name}`}
                    className="text-accent hover:underline"
                  >
                    Learn More →
                  </Link>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Latest Blog Posts */}
      <section className="bg-primary/10 py-20">
        <div className="container mx-auto px-4">
          <div className="mb-8 flex items-center justify-between">
            <h2 className="text-3xl font-bold text-primary">
              Latest Blog Posts
            </h2>
            <Link href="/blog" className="text-accent hover:underline">
              View All →
            </Link>
          </div>
          <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
            {latestPosts.map((post) => (
              <Link
                key={post.id}
                href={`/blog/${post.id}`}
                className="transform overflow-hidden rounded-lg bg-secondary shadow-lg transition hover:-translate-y-1 hover:shadow-xl"
              >
                <div className="relative h-48 bg-gradient-to-r from-dark-orange to-bright-purple">
                  {post.coverImage ? (
                    <Image
                      src={post.coverImage}
                      alt={post.title}
                      fill
                      className="object-cover"
                    />
                  ) : (
                    <div className="absolute inset-0 flex items-center justify-center text-center text-6xl text-white text-opacity-30">
                      {post.title}
                    </div>
                  )}
                </div>
                <div className="p-6">
                  <h2 className="mb-2 line-clamp-2 text-xl font-bold text-primary">
                    {post.title}
                  </h2>
                  <div className="mb-4 flex items-center text-xs text-secondary">
                    <span>
                      {post.createdAt.toLocaleDateString(undefined, {
                        year: "numeric",
                        month: "long",
                        day: "numeric",
                      })}
                    </span>
                    {post.author && (
                      <>
                        <span className="mx-2">•</span>
                        <span>{post.author}</span>
                      </>
                    )}
                  </div>
                  <div
                    className="prose prose-sm prose-invert mb-4 line-clamp-3 max-w-none text-secondary"
                    dangerouslySetInnerHTML={{
                      __html: post.content?.substring(0, 150) + "..." || "",
                    }}
                  />
                  <span className="text-accent hover:underline">
                    Read more →
                  </span>
                </div>
              </Link>
            ))}
          </div>
        </div>
      </section>

      {/* Additional Sections */}
    </main>
  );
}
