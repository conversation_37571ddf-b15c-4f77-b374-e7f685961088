import { notFound } from "next/navigation";
import Image from "next/image";
import Link from "next/link";
import { getAllBlogPostIds, getBlogPost } from "@/lib/blog";


interface PageProps {
  params: {
    id: string;
  };
}

export async function generateStaticParams() {
  console.log("generateStaticParams called for /blog/[id]");
  try {
    const params = await getAllBlogPostIds();
    console.log("Params generated:", params);
    return params;
  } catch (error) {
    console.error("Error during generateStaticParams execution:", error);
    // Return a placeholder to prevent build failure
    return [{ id: "placeholder" }];
  }
}

export default async function BlogPostPage({ params }: PageProps) {
  // eslint-disable-next-line
  const { id } = await params;
  if (!id) {
    notFound();
  }
  const post = await getBlogPost(id);

  if (!post) {
    notFound();
  }

  return (
    <div className="container mx-auto px-4 py-12">
      <div className="mx-auto max-w-4xl">
        {post.isTeamBlog && (
          <div className="mb-8 rounded-xl bg-secondary p-4 text-center text-secondary">
            <p className="mb-2 text-xl font-bold">Team Blog Post</p>
            <p>Exclusive insights from our team members.</p>
          </div>
        )}
        {post.isTeamBlog ? (
          <Link
            href="/team-blog"
            className="mb-8 inline-flex items-center text-secondary hover:text-accent"
          >
            ← Back to all team posts
          </Link>
        ) : (
          <Link
            href="/blog"
            className="mb-8 inline-flex items-center text-secondary hover:text-accent"
          >
            ← Back to all posts
          </Link>
        )}

        <h1 className="mb-6 text-4xl font-bold text-primary md:text-5xl">
          {post.title}
        </h1>

        <div className="mb-8 flex items-center text-secondary">
          <span>
            {post.createdAt.toLocaleDateString(undefined, {
              year: "numeric",
              month: "long",
              day: "numeric",
            })}
          </span>
          {post.author && (
            <>
              <span className="mx-2">•</span>
              <span>{post.author}</span>
            </>
          )}
          {post.createdAt.getTime() !== post.updatedAt.getTime() && (
            <>
              <span className="mx-2">•</span>
              <span>Updated on {post.updatedAt.toLocaleDateString()}</span>
            </>
          )}
        </div>

        {post.coverImage && (
          <div className="mb-8 overflow-hidden rounded-xl">
            <Image
              src={post.coverImage}
              alt={post.title}
              width={1200}
              height={675}
              className="w-full object-cover"
            />
          </div>
        )}

        <div
          className="prose prose-lg prose-invert max-w-none"
          dangerouslySetInnerHTML={{ __html: post.content ?? "" }}
        />

        <div className="border-border-primary mt-12 border-t pt-8">
          <h2 className="mb-4 text-2xl font-bold text-primary">
            Share this post
          </h2>
          <div className="flex space-x-4">
            {[
              { name: "X" },
              { name: "Facebook" },
              { name: "LinkedIn" },
              { name: "Copy" },
            ].map((social) => (
              <button
                key={social.name}
                className="bg-primary/30 hover:bg-accent/20 flex h-10 w-10 items-center justify-center rounded-full transition"
                aria-label={`Share on ${social.name}`}
              >
                <Image
                  src={`/images/socials/${social.name.toLowerCase()}-logo.svg`}
                  alt={social.name}
                  width={20}
                  height={20}
                />
                <span className="sr-only">{social.name}</span>
              </button>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
