import {
  collection,
  doc,
  getDoc,
  getDocs,
  limit,
  orderBy,
  query,
  where,
} from "firebase/firestore";
import { db } from "@/lib/firebase";

export interface BlogPost {
  id: string;
  author?: string;
  title: string;
  content?: string;
  coverImage?: string;
  createdAt: Date;
  updatedAt: Date;
  isPublic: boolean;
  isTeamBlog?: boolean;
}

interface FirestoreData {
  author?: string;
  title: string;
  content?: string;
  coverImage?: string;
  createdAt: { toDate(): Date };
  updatedAt: { toDate(): Date };
  isPublic: boolean;
  isTeamBlog?: boolean;
}

export async function getLatestBlogPosts(): Promise<BlogPost[]> {
  try {
    const q = query(
      collection(db, "posts"),
      where("isPublic", "==", true),
      where("isTeamBlog", "!=", true), // Exclude team blogs
      orderBy("createdAt", "desc"),
      limit(3), // Limit to 3 latest posts
    );
    const snapshot = await getDocs(q);
    return snapshot.docs.map((doc) => ({
      id: doc.id,
      author: doc.data().author as string | undefined,
      title: doc.data().title as string,
      content: doc.data().content as string | undefined,
      coverImage: doc.data().coverImage as string | undefined,
      // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access ,@typescript-eslint/no-unsafe-call,@typescript-eslint/no-unsafe-assignment
      createdAt: doc.data().createdAt?.toDate() ?? new Date(),
      isPublic: doc.data().isPublic as boolean,
      isTeamBlog: doc.data().isTeamBlog as boolean | undefined,
    })) as BlogPost[];
  } catch (error) {
    console.error("Error fetching latest blog posts:", error);
    return [];
  }
}

export async function getAllBlogPostIds() {
  try {
    const q = query(
      collection(db, "posts"),
      where("isPublic", "==", true),
      orderBy("createdAt", "desc"),
    );
    const snapshot = await getDocs(q);
    const params = snapshot.docs.map((doc) => ({
      id: doc.id,
    }));

    console.log("Successfully fetched IDs:", params);

    // Important: Return at least one parameter object even if array is empty
    if (params.length === 0) {
      console.log("No blog posts found, adding placeholder parameter");
      return [{ id: "placeholder" }];
    }

    return params;
  } catch (error) {
    console.error("Error fetching blog post IDs for params:", error);
    // Return at least one placeholder ID to prevent build failure
    return [{ id: "placeholder" }];
  }
}

export async function getBlogPosts(): Promise<BlogPost[]> {
  try {
    const q = query(
      collection(db, "posts"),
      where("isPublic", "==", true),
      orderBy("createdAt", "desc"),
    );

    const snapshot = await getDocs(q);

    return snapshot.docs.map((doc) => {
      const data = doc.data();
      return {
        id: doc.id,
        author: data.author as string | undefined,
        title: data.title as string,
        content: data.content as string | undefined,
        coverImage: data.coverImage as string | undefined,
        // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment,@typescript-eslint/no-unsafe-call,@typescript-eslint/no-unsafe-member-access
        createdAt: data.createdAt?.toDate() ?? new Date(),
        isPublic: data.isPublic as boolean,
        isTeamBlog: data.isTeamBlog as boolean | undefined,
      };
    }) as BlogPost[];
  } catch (error) {
    console.error("Error fetching blog posts:", error);
    return [];
  }
}

export async function getBlogPost(id: string): Promise<BlogPost | null> {
  try {
    // Special handling for placeholder during build
    if (id === "placeholder") {
      return {
        id: "placeholder",
        title: "Coming Soon",
        content: "<p>Our blog posts are coming soon!</p>",
        createdAt: new Date(),
        updatedAt: new Date(),
        isPublic: true,
      };
    }

    const docRef = id ? doc(db, "posts", id) : null;
    if (!docRef) return null;
    const docSnap = await getDoc(docRef);

    if (docSnap.exists() && docSnap.data()) {
      const data = docSnap.data() as FirestoreData;
      if (data.isPublic || data.isTeamBlog) {
        return {
          id: docSnap.id,
          author: data.author,
          title: data.title,
          content: data.content,
          coverImage: data.coverImage,
          createdAt: data.createdAt.toDate(),
          updatedAt: data.updatedAt.toDate(),
          isPublic: data.isPublic,
          isTeamBlog: data.isTeamBlog,
        };
      }
    }

    return null;
  } catch (error) {
    console.error("Error fetching blog post:", error);
    return null;
  }
}
