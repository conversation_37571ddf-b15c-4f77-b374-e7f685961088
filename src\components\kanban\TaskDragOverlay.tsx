import React from "react";
import type { KanbanTask } from "@/types/kanban";
import TaskCard from "./TaskCard";

interface TaskDragOverlayProps {
  task: KanbanTask;
}

const TaskDragOverlay: React.FC<TaskDragOverlayProps> = ({ task }) => {
  return (
    <div className="rotate-2 scale-105">
      <TaskCard task={task} onClick={() => {}} isDragOverlay />
    </div>
  );
};

export default TaskDragOverlay;
