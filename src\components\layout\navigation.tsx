"use client";

import { useEffect, useState } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useAuth } from "@/lib/auth-context";
import { useTheme } from "next-themes";
import GetLogo from "@/components/layout/get-logo";

export default function Navigation() {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [mounted, setMounted] = useState(false);
  const pathname = usePathname();
  const { user, loading } = useAuth();
  const { theme, setTheme } = useTheme();

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };

    setMounted(true);
    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  const links = [
    { name: "Home", href: "/" },
    { name: "About", href: "/about" },
    {
      name: "Join The Team",
      href: "/join-us",
    },
    { name: "<PERSON>", href: "/games" },
    { name: "Roadmap", href: "/roadmap" },
    { name: "Blog", href: "/blog" },
    {
      name: "Donate",
      href: "https://discord.com/servers/parallax-interactive-1036739036427071498",
      target: "_blank",
    },
  ];

  const isPrivatePage = pathname?.includes("/(private)");

  return (
    <header
      className={`fixed left-0 right-0 top-0 z-50 transition-all duration-300 ${
        isScrolled || isPrivatePage
          ? "bg-primary py-3 shadow-md backdrop-blur-sm"
          : "bg-primary py-5"
      }`}
    >
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between">
          <Link href="/" className="flex items-center">
            <GetLogo />
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden items-center space-x-8 lg:flex">
            {links.map((link) => (
              <Link
                key={link.name}
                href={link.href}
                target={link?.target ?? undefined}
                className={`text-sm font-medium transition-colors ${
                  pathname === link.href
                    ? "text-accent"
                    : "text-primary hover:text-accent"
                }`}
              >
                {link.name}
              </Link>
            ))}
            {mounted && (
              <button
                onClick={() => setTheme(theme === "dark" ? "light" : "dark")}
                className="rounded-md p-2 text-secondary hover:text-primary"
                aria-label="Toggle theme"
              >
                {theme === "dark" ? "🌞" : "🌙"}
              </button>
            )}
            {!loading && (
              <>
                {user ? (
                  <Link
                    href="/dashboard"
                    className="rounded-lg bg-accent px-4 py-2 text-sm font-medium text-white transition hover:opacity-90"
                  >
                    Dashboard
                  </Link>
                ) : (
                  <Link
                    href="/login"
                    className="border-border-primary hover:bg-primary/10 rounded-lg border px-4 py-2 text-sm font-medium text-primary transition"
                  >
                    Developer Login
                  </Link>
                )}
              </>
            )}
          </nav>

          {/* Mobile Menu Button */}
          <button
            aria-label="Toggle menu"
            type="button"
            className="p-2 text-primary lg:hidden"
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-6 w-6"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              {isMobileMenuOpen ? (
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              ) : (
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4 6h16M4 12h16M4 18h16"
                />
              )}
            </svg>
          </button>
        </div>

        {/* Mobile Navigation */}
        {isMobileMenuOpen && (
          <nav className="mt-2 space-y-3 py-4 md:hidden">
            {links.map((link) => (
              <Link
                key={link.name}
                href={link.href}
                className={`block py-2 text-base font-medium ${
                  pathname === link.href
                    ? "text-accent"
                    : "text-primary hover:text-accent"
                }`}
                onClick={() => setIsMobileMenuOpen(false)}
              >
                {link.name}
              </Link>
            ))}

            {!loading && (
              <>
                {user ? (
                  <Link
                    href={isPrivatePage ? "/" : "/dashboard"}
                    className="block py-2 text-base font-medium text-accent hover:underline"
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    {isPrivatePage ? "Public Site" : "Dashboard"}
                  </Link>
                ) : (
                  <Link
                    href="/login"
                    className="block py-2 text-base font-medium text-primary hover:text-accent"
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    Developer Login
                  </Link>
                )}
              </>
            )}
          </nav>
        )}
      </div>
    </header>
  );
}
