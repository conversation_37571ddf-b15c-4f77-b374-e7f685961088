{"name": "parallax-interactive", "version": "0.1.0", "private": true, "type": "module", "scripts": {"build": "next build", "check": "next lint && tsc --noEmit", "dev": "next dev --turbo", "lint": "next lint", "lint:fix": "next lint --fix", "preview": "next build && next start", "start": "next start", "typecheck": "tsc --noEmit", "format:write": "prettier --write \"**/*.{ts,tsx,js,jsx,mdx}\" --cache", "format:check": "prettier --check \"**/*.{ts,tsx,js,jsx,mdx}\" --cache"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^6.4.6", "@mui/material": "^6.4.6", "@phosphor-icons/react": "^2.1.7", "@radix-ui/react-slot": "^1.1.2", "@t3-oss/env-nextjs": "^0.10.1", "@tiptap/extension-image": "^2.11.5", "@tiptap/extension-placeholder": "^2.11.5", "@tiptap/react": "^2.11.5", "@tiptap/starter-kit": "^2.11.5", "@toast-ui/editor": "^3.2.2", "autoprefixer": "^10.4.20", "class-variance-authority": "^0.7.1", "codemirror": "^6.0.1", "date-fns": "^4.1.0", "firebase": "^11.5.0", "framer-motion": "^12.4.7", "geist": "^1.3.0", "lucide-react": "^0.513.0", "next": "^15.2.0", "next-themes": "^0.4.4", "nodemailer": "^6.10.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-firebase-hooks": "^5.1.1", "react-markdown": "^10.0.0", "react-quill": "^2.0.0", "react-syntax-highlighter": "^15.6.1", "remark-gfm": "^4.0.1", "tailwind-merge": "^3.0.2", "uuid": "^11.1.0", "zod": "^3.23.3"}, "devDependencies": {"@tailwindcss/typography": "^0.5.16", "@types/eslint": "^8.56.10", "@types/node": "^20.14.10", "@types/nodemailer": "^6.4.17", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@types/react-syntax-highlighter": "^15.5.13", "@typescript-eslint/eslint-plugin": "^8.1.0", "@typescript-eslint/parser": "^8.1.0", "eslint": "^8.57.0", "eslint-config-next": "^15.0.1", "postcss": "^8.5.3", "prettier": "^3.3.2", "prettier-plugin-tailwindcss": "^0.6.5", "tailwindcss": "^3.4.17", "typescript": "^5.5.3"}, "ct3aMetadata": {"initVersion": "7.38.1"}, "packageManager": "npm@11.1.0"}