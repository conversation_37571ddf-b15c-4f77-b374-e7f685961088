"use client";

import React, { useState } from "react";

export default function CareersPage() {
  const [name, setName] = useState("");
  const [email, setEmail] = useState("");
  const [position, setPosition] = useState("");
  const [resume, setResume] = useState<File | null>(null);
  const [message, setMessage] = useState("");
  const [successMessage, setSuccessMessage] = useState("");
  const [errorMessage, setErrorMessage] = useState("");
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setSuccessMessage("");
    setErrorMessage("");

    if (!name || !email || !position || !resume) {
      setErrorMessage("Please fill in all required fields.");
      setLoading(false);
      return;
    }

    try {
      const formData = new FormData();
      formData.append("name", name);
      formData.append("email", email);
      formData.append("position", position);
      formData.append("resume", resume);
      formData.append("message", message);

      const response = await fetch("/api/send-email", {
        method: "POST",
        body: formData,
      });

      if (response.ok) {
        setSuccessMessage("Your application has been sent successfully!");
        setName("");
        setEmail("");
        setPosition("");
        setResume(null);
        setMessage("");
      } else {
        interface ErrorResponse {
          error?: string;
        }

        const errorData = (await response.json()) as ErrorResponse;
        setErrorMessage(
          `Failed to send application: ${errorData.error ?? "Unknown error"}`,
        );
      }
    } catch (error) {
      console.error("Error sending application:", error);
      setErrorMessage("Failed to send application. Please try again later.");
    } finally {
      setLoading(false);
    }
  };

  const handleResumeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files?.[0]) {
      setResume(e.target.files[0]);
    }
  };

  return (
    <div className="container mx-auto px-4 py-12">
      <div className="mb-16 text-center">
        <h1 className="mb-4 text-4xl font-bold text-primary">Join Our Team</h1>
        <p className="mx-auto max-w-2xl text-secondary">
          We&#39;re always looking for talented and passionate individuals to
          join our team.
        </p>
      </div>

      <div className="mx-auto max-w-3xl rounded-xl bg-secondary p-8 shadow-lg">
        <h2 className="mb-6 text-2xl font-bold text-primary">Apply Now</h2>

        {successMessage && (
          <div className="mb-4 rounded border border-green-500 bg-green-500/10 p-3 text-sm text-green-500">
            {successMessage}
          </div>
        )}

        {errorMessage && (
          <div className="mb-4 rounded border border-red-500 bg-red-500/10 p-3 text-sm text-red-500">
            {errorMessage}
          </div>
        )}

        <form onSubmit={handleSubmit}>
          <div className="mb-4">
            <label
              htmlFor="name"
              className="mb-1 block text-sm font-medium text-secondary"
            >
              Your Name
            </label>
            <input
              type="text"
              id="name"
              value={name}
              onChange={(e) => setName(e.target.value)}
              className="border-border-primary w-full rounded-lg border bg-primary px-4 py-2 text-primary focus:outline-none focus:ring-2 focus:ring-primary"
              required
            />
          </div>

          <div className="mb-4">
            <label
              htmlFor="email"
              className="mb-1 block text-sm font-medium text-secondary"
            >
              Email Address
            </label>
            <input
              type="email"
              id="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="border-border-primary w-full rounded-lg border bg-primary px-4 py-2 text-primary focus:outline-none focus:ring-2 focus:ring-primary"
              required
            />
          </div>

          <div className="mb-4">
            <label
              htmlFor="position"
              className="mb-1 block text-sm font-medium text-secondary"
            >
              Position Applying For
            </label>
            <input
              type="text"
              id="position"
              value={position}
              onChange={(e) => setPosition(e.target.value)}
              className="border-border-primary w-full rounded-lg border bg-primary px-4 py-2 text-primary focus:outline-none focus:ring-2 focus:ring-primary"
              required
            />
          </div>

          <div className="mb-4">
            <label
              htmlFor="resume"
              className="mb-1 block text-sm font-medium text-secondary"
            >
              Upload Resume
            </label>
            <input
              type="file"
              id="resume"
              onChange={handleResumeChange}
              className="border-border-primary w-full rounded-lg border bg-primary px-4 py-2 text-primary focus:outline-none focus:ring-2 focus:ring-primary"
              required
            />
          </div>

          <div className="mb-6">
            <label
              htmlFor="message"
              className="mb-1 block text-sm font-medium text-secondary"
            >
              Additional Message
            </label>
            <textarea
              id="message"
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              rows={4}
              className="border-border-primary w-full rounded-lg border bg-primary px-4 py-2 text-primary focus:outline-none focus:ring-2 focus:ring-primary"
            ></textarea>
          </div>

          <button
            type="submit"
            disabled={loading}
            className="w-full rounded-lg bg-accent py-3 font-medium text-white transition hover:opacity-90 disabled:cursor-not-allowed disabled:opacity-70"
          >
            {loading ? "Sending..." : "Send Application"}
          </button>
        </form>
      </div>
    </div>
  );
}
