import {
  collection,
  addDoc,
  getDocs,
  doc,
  updateDoc,
  deleteDoc,
  query,
  orderBy,
  Timestamp,
  writeBatch,
  serverTimestamp,
  arrayUnion,
} from "firebase/firestore";
import { db } from "../firebase";
import type {
  KanbanTask,
  KanbanColumn,
  UserProfile,
  TaskNote,
} from "@/types/kanban";
import { v4 as uuidv4 } from "uuid";

export const getKanbanColumns = async (): Promise<KanbanColumn[]> => {
  const q = query(collection(db, "kanban_columns"), orderBy("order"));
  const snapshot = await getDocs(q);
  return snapshot.docs.map(
    (doc) => ({ id: doc.id, ...doc.data() }) as KanbanColumn,
  );
};

export const addColumn = async (
  columnData: Omit<KanbanColumn, "id">,
): Promise<string> => {
  const docRef = await addDoc(collection(db, "kanban_columns"), columnData);
  return docRef.id;
};

export const updateColumn = async (
  columnId: string,
  updates: Partial<KanbanColumn>,
): Promise<void> => {
  const docRef = doc(db, "kanban_columns", columnId);
  await updateDoc(docRef, updates);
};

export const deleteColumn = async (columnId: string): Promise<void> => {
  const docRef = doc(db, "kanban_columns", columnId);
  await deleteDoc(docRef);
};

export const updateColumnOrderBatch = async (
  updates: { id: string; order: number }[],
): Promise<void> => {
  const batch = writeBatch(db);
  updates.forEach((update) => {
    const docRef = doc(db, "kanban_columns", update.id);
    batch.update(docRef, { order: update.order });
  });
  await batch.commit();
};

export const deleteTasksByColumn = async (columnId: string): Promise<void> => {
  const tasksQuery = query(collection(db, "kanban_tasks"));
  const tasksSnapshot = await getDocs(tasksQuery);
  const batch = writeBatch(db);

  tasksSnapshot.docs.forEach((taskDoc) => {
    const task = taskDoc.data();
    if (task.columnId === columnId) {
      batch.delete(taskDoc.ref);
    }
  });

  await batch.commit();
};

export const getKanbanTasks = async (): Promise<KanbanTask[]> => {
  const q = query(collection(db, "kanban_tasks"), orderBy("order"));
  const snapshot = await getDocs(q);
  return snapshot.docs.map(
    (doc) =>
      ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt as Timestamp,
        updatedAt: doc.data().updatedAt as Timestamp,
        priority: doc.data().priority || "medium",
      }) as KanbanTask,
  );
};

export const addTask = async (
  taskData: Omit<KanbanTask, "id" | "createdAt" | "updatedAt" | "notes">,
): Promise<string> => {
  const docRef = await addDoc(collection(db, "kanban_tasks"), {
    ...taskData,
    priority: taskData.priority || "medium",
    createdAt: serverTimestamp(),
    updatedAt: serverTimestamp(),
    notes: [],
  });
  return docRef.id;
};

export const updateTask = async (
  taskId: string,
  updates: Partial<KanbanTask>,
): Promise<void> => {
  const taskRef = doc(db, "kanban_tasks", taskId);
  await updateDoc(taskRef, { ...updates, updatedAt: serverTimestamp() });
};

export const deleteTask = async (taskId: string): Promise<void> => {
  const taskRef = doc(db, "kanban_tasks", taskId);
  await deleteDoc(taskRef);
};

export const updateTaskOrderBatch = async (
  tasksToUpdate: { id: string; order: number; columnId?: string }[],
): Promise<void> => {
  const batch = writeBatch(db);
  tasksToUpdate.forEach((taskUpdate) => {
    const taskRef = doc(db, "kanban_tasks", taskUpdate.id);
    const updateData: Partial<KanbanTask> = { order: taskUpdate.order };
    if (taskUpdate.columnId) {
      updateData.columnId = taskUpdate.columnId;
    }
    updateData.updatedAt = serverTimestamp() as Timestamp;
    batch.update(taskRef, updateData);
  });
  await batch.commit();
};

export const getAssignableUsers = async (): Promise<UserProfile[]> => {
  try {
    const usersCollection = collection(db, "users");
    const snapshot = await getDocs(usersCollection);
    if (snapshot.empty) {
      console.warn("No users found in 'users' collection for task assignment.");
      return [];
    }
    return snapshot.docs.map(
      (doc) =>
        ({
          uid: doc.id,
          ...doc.data(),
        }) as UserProfile,
    );
  } catch (error) {
    console.error("Error fetching assignable users:", error);
    return [];
  }
};

export const addTaskNote = async (
  taskId: string,
  noteText: string,
  userId: string,
  userName?: string,
): Promise<TaskNote> => {
  const taskRef = doc(db, "kanban_tasks", taskId);
  const newNote: TaskNote = {
    id: uuidv4(),
    text: noteText,
    userId,
    userName: userName || "User",
    timestamp: Timestamp.now(),
  };
  await updateDoc(taskRef, {
    notes: arrayUnion(newNote),
    updatedAt: serverTimestamp(),
  });
  return newNote;
};
