import Link from "next/link";
import Image from "next/image";
import { getBlogPosts } from "@/lib/blog";

export default async function TeamBlogPage() {
  const posts = await getBlogPosts();

  return (
    <div className="container mx-auto px-4 py-12">
      <div className="mb-16 text-center">
        <h1 className="mb-4 text-4xl font-bold text-primary">Team Blog</h1>
        <p className="mx-auto max-w-2xl text-secondary">
          Insights, updates, and behind-the-scenes looks at our game development
          process.
        </p>
      </div>

      <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
        {posts.length === 0 ? (
          <div className="col-span-full py-16 text-center text-secondary">
            <p className="mb-2 text-xl">No Team blog posts yet</p>
            <p>
              Check back soon for updates on our projects and development
              insights.
            </p>
          </div>
        ) : (
          posts.map(
            (post) =>
              post.isTeamBlog && (
                <Link
                  key={post.id}
                  href={`/blog/${post.id}`}
                  className="transform overflow-hidden rounded-xl bg-secondary shadow-lg transition hover:-translate-y-1 hover:shadow-xl"
                >
                  <div className="relative h-48 bg-gradient-to-r from-dark-orange to-bright-purple">
                    {post.coverImage ? (
                      <Image
                        src={post.coverImage}
                        alt={post.title}
                        fill
                        className="object-cover"
                      />
                    ) : (
                      <div className="absolute inset-0 flex items-center justify-center text-center text-6xl text-white text-opacity-30">
                        {post.title}
                      </div>
                    )}
                  </div>
                  <div className="p-6">
                    <h2 className="mb-2 line-clamp-2 text-xl font-bold text-primary">
                      {post.title}
                    </h2>
                    <div className="mb-4 flex items-center text-xs text-secondary">
                      <span>
                        {post.createdAt.toLocaleDateString(undefined, {
                          year: "numeric",
                          month: "long",
                          day: "numeric",
                        })}
                      </span>
                      {post.author && (
                        <>
                          <span className="mx-2">•</span>
                          <span>{post.author}</span>
                        </>
                      )}
                    </div>
                    <div
                      className="prose prose-sm prose-invert mb-4 line-clamp-3 max-w-none text-secondary"
                      dangerouslySetInnerHTML={{
                        __html: post.content?.substring(0, 150) + "..." || "",
                      }}
                    />
                    <span className="text-accent hover:underline">
                      Catch Up →
                    </span>
                  </div>
                </Link>
              ),
          )
        )}
      </div>
    </div>
  );
}
